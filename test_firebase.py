#!/usr/bin/env python3
"""
Test Firebase credentials and connection
"""

import os
import sys
import json
import base64
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_firebase_credentials():
    """Test Firebase credentials parsing"""
    print("🔥 Testing Firebase credentials...")
    
    firebase_creds = os.getenv("FIREBASE_CREDENTIALS")
    if not firebase_creds:
        print("❌ FIREBASE_CREDENTIALS not found in environment")
        return False
    
    print(f"✅ Found Firebase credentials ({len(firebase_creds)} characters)")
    
    try:
        # Test if it's base64 encoded
        if not firebase_creds.startswith('{'):
            print("🔍 Detected base64 encoded credentials, decoding...")
            decoded = base64.b64decode(firebase_creds).decode('utf-8')
            creds_dict = json.loads(decoded)
        else:
            print("🔍 Detected JSON credentials, parsing...")
            creds_dict = json.loads(firebase_creds)
        
        # Verify required fields
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = []
        
        for field in required_fields:
            if field not in creds_dict:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return False
        
        print("✅ All required fields present:")
        print(f"  - Project ID: {creds_dict['project_id']}")
        print(f"  - Client Email: {creds_dict['client_email']}")
        print(f"  - Type: {creds_dict['type']}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error parsing credentials: {e}")
        return False

def test_firebase_connection():
    """Test Firebase connection"""
    print("\n🔗 Testing Firebase connection...")
    
    try:
        # Import Firebase modules
        import firebase_admin
        from firebase_admin import credentials, db
        
        # Check if already initialized
        if firebase_admin._apps:
            print("✅ Firebase already initialized")
            return True
        
        # Get credentials
        firebase_creds = os.getenv("FIREBASE_CREDENTIALS")
        firebase_url = os.getenv("FIREBASE_DATABASE_URL")
        
        if not firebase_url:
            print("❌ FIREBASE_DATABASE_URL not found")
            return False
        
        print(f"✅ Database URL: {firebase_url}")
        
        # Parse credentials
        if not firebase_creds.startswith('{'):
            decoded = base64.b64decode(firebase_creds).decode('utf-8')
            creds_dict = json.loads(decoded)
        else:
            creds_dict = json.loads(firebase_creds)
        
        # Initialize Firebase
        cred = credentials.Certificate(creds_dict)
        firebase_admin.initialize_app(cred, {"databaseURL": firebase_url})
        
        print("✅ Firebase initialized successfully")
        
        # Test basic operation
        ref = db.reference("test_connection")
        test_data = {"timestamp": "test", "status": "connected"}
        ref.set(test_data)
        
        # Read back the data
        read_data = ref.get()
        if read_data and read_data.get("status") == "connected":
            print("✅ Firebase read/write test successful")
            return True
        else:
            print("❌ Firebase read/write test failed")
            return False
            
    except ImportError as e:
        print(f"❌ Firebase modules not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Firebase connection error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Firebase Configuration Test")
    print("=" * 40)
    
    success = True
    
    # Test credentials parsing
    if not test_firebase_credentials():
        success = False
    
    # Test Firebase connection
    if not test_firebase_connection():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All Firebase tests passed!")
        return 0
    else:
        print("❌ Some Firebase tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
