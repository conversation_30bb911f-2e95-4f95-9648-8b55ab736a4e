# Git and version control
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Development files
.env.example
.vscode/
.idea/
*.pyc
__pycache__/
.pytest_cache/
.coverage
.tox/

# Logs (will be created in container)
logs/*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files
Dockerfile
docker-compose.yml
.dockerignore
build-and-push.sh

# Test files
test_*.py
tests/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Node modules (if any)
node_modules/

# Python virtual environments
venv/
env/
.venv/
.env/

# IDE files
*.sublime-*
*.code-workspace

# Build artifacts
build/
dist/
*.egg-info/

# Railway specific (keep railway.toml)
# railway.toml should be included for Railway deployment
