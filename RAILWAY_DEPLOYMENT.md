# 🚂 Railway Deployment Guide for Wiz Aroma Food Delivery System

This guide will help you deploy your Wiz Aroma Food Delivery System to Railway, ensuring it runs directly without Docker complications.

## 📋 Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **GitHub Repository**: Your code should be pushed to GitHub
3. **Bot Tokens**: All 7 Telegram bot tokens from @BotFather
4. **Firebase Setup**: Firebase project with Realtime Database

## 🚀 Deployment Steps

### Step 1: Prepare Your Repository

Ensure these files are in your repository root:
- ✅ `railway.json` - Railway configuration
- ✅ `nixpacks.toml` - Build configuration  
- ✅ `Procfile` - Process definition
- ✅ `start.py` - Startup script
- ✅ `runtime.txt` - Python version
- ✅ `.env.example` - Environment template

### Step 2: Create Railway Project

1. Go to [railway.app](https://railway.app)
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose your repository

### Step 3: Configure Environment Variables

In Railway dashboard, go to Variables tab and add:

#### 🤖 Bot Tokens
```
BOT_TOKEN=your_main_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token
FINANCE_BOT_TOKEN=your_finance_bot_token
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
MANAGEMENT_BOT_TOKEN=your_management_bot_token
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token
DELIVERY_BOT_TOKEN=your_delivery_bot_token
```

#### 🔐 Authorization
```
SYSTEM_ADMIN_ID=your_telegram_user_id
ADMIN_CHAT_IDS=["your_admin_id"]
ORDER_TRACK_BOT_AUTHORIZED_IDS=["your_admin_id"]
DELIVERY_BOT_AUTHORIZED_IDS=["your_admin_id"]
MANAGEMENT_BOT_AUTHORIZED_IDS=["your_admin_id"]
```

#### 🔥 Firebase Configuration
```
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id",...}
```

#### 💳 Payment Information
```
TELEBIRR_PHONE=+************
TELEBIRR_NAME=Your Business Name
CBE_ACCOUNT_NUMBER=****************
CBE_ACCOUNT_NAME=Your Business Name
BOA_ACCOUNT_NUMBER=****************
BOA_ACCOUNT_NAME=Your Business Name
```

#### 📞 Contact Information
```
SUPPORT_PHONE_1=+************
SUPPORT_PHONE_2=+************
SUPPORT_TELEGRAM=@your_support_username
CUSTOMER_SERVICE_PHONE=+************
CUSTOMER_SERVICE_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>
```

#### ⚙️ Application Settings
```
RAILWAY_ENVIRONMENT=production
NODE_ENV=production
LOG_LEVEL=INFO
TEST_MODE=False
```

### Step 4: Deploy

1. Railway will automatically detect the configuration files
2. It will use Nixpacks (not Docker) to build your application
3. The build process will:
   - Install Python 3.9
   - Install dependencies from `requirements.txt`
   - Start the application using `start.py`

### Step 5: Monitor Deployment

1. Check the deployment logs in Railway dashboard
2. Look for these success messages:
   ```
   🚀 Starting Wiz Aroma Food Delivery System...
   📡 Detected Railway environment
   ✅ Dependencies verified
   🤖 Starting bot system...
   Health check server started for cloud deployment
   ```

3. Your application will be available at the Railway-provided URL
4. Health check endpoint: `https://your-app.railway.app/health`

## 🔧 Troubleshooting

### Common Issues

#### 1. Build Fails
- Check that all files are committed to GitHub
- Verify `requirements.txt` is present and valid
- Check Railway build logs for specific errors

#### 2. Environment Variables Missing
- Ensure all required variables are set in Railway dashboard
- Check variable names match exactly (case-sensitive)
- For `FIREBASE_CREDENTIALS`, use single-line JSON format

#### 3. Bot Tokens Invalid
- Verify tokens are from @BotFather
- Check token format: `XXXXXXXXXX:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX`
- Ensure no extra spaces or characters

#### 4. Firebase Connection Issues
- Verify Firebase Database URL is correct
- Check Firebase credentials JSON is valid
- Ensure Firebase project has Realtime Database enabled

### Logs and Monitoring

1. **Railway Logs**: Check deployment and runtime logs in Railway dashboard
2. **Health Check**: Visit `/health` endpoint to verify application is running
3. **Status Check**: Visit `/status` endpoint for detailed application status

## 🔄 Updates and Redeployment

1. Push changes to your GitHub repository
2. Railway will automatically redeploy
3. Monitor logs during redeployment
4. Verify health check endpoint after deployment

## 📞 Support

If you encounter issues:
1. Check Railway documentation
2. Review application logs
3. Verify all environment variables are set correctly
4. Test bot tokens with @BotFather

## 🧪 Testing Before Deployment

Run the deployment readiness test:
```bash
python test_deployment.py
```

This will verify:
- ✅ All required files are present
- ✅ Configuration files are valid
- ✅ Dependencies are specified correctly
- ✅ Railway configuration is proper

## 🎉 Success!

Once deployed successfully, your Wiz Aroma Food Delivery System will be running on Railway with:
- ✅ All 7 specialized bots operational
- ✅ Firebase integration working
- ✅ Health monitoring active
- ✅ Automatic restarts on failure
- ✅ Professional logging and monitoring

## 🔍 Verification

After deployment, verify your system:

1. **Health Check**: Visit `https://your-app.railway.app/health`
   ```json
   {
     "status": "healthy",
     "timestamp": **********,
     "service": "wiz-aroma-bot"
   }
   ```

2. **Status Check**: Visit `https://your-app.railway.app/status`
   ```json
   {
     "status": "running",
     "service": "Wiz Aroma Food Delivery Bot",
     "version": "2.1",
     "timestamp": **********,
     "uptime": 3600
   }
   ```

3. **Bot Functionality**: Test your bots on Telegram
   - Send `/start` to your main bot
   - Verify admin bots respond to authorized users
   - Check Firebase data is being read/written

## 📊 Monitoring

Railway provides built-in monitoring:
- **Metrics**: CPU, Memory, Network usage
- **Logs**: Real-time application logs
- **Deployments**: Deployment history and status
- **Health Checks**: Automatic health monitoring
