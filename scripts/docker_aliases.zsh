#!/usr/bin/env zsh
# Helper shell function to simplify building linux/amd64 Docker images
# Usage:
#   dcbamd64 <image-name[:tag]> [docker-build-args...] [context]
# Example:
#   dcbamd64 wiz-aroma-food-delivery-astu:latest .
# Runs:
#   docker build --platform linux/amd64 -t fedbish/wiz-aroma-food-delivery-astu:latest .

if [[ -n $ZSH_VERSION ]]; then
  # Define the function for interactive use
  dcbamd64() {
    if [[ $# -lt 1 ]]; then
      echo "Usage: dcbamd64 <image-name[:tag]> [docker-build-args...] [context]"
      return 1
    fi

    local image="$1"
    shift

    # Allow passing any additional docker build args and context
    docker build --platform linux/amd64 -t "fedbish/${image}" "$@"
  }
else
  echo "Warning: this file is intended to be sourced by zsh (interactive shell)." >&2
fi
