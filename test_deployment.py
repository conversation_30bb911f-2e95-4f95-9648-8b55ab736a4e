#!/usr/bin/env python3
"""
Test script to verify deployment readiness for Wiz Aroma Food Delivery System
"""

import os
import sys
import json
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a file exists and report status"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_env_example():
    """Check .env.example file"""
    if not check_file_exists('.env.example', 'Environment template'):
        return False
    
    try:
        with open('.env.example', 'r') as f:
            content = f.read()
            required_vars = [
                'BOT_TOKEN', 'ADMIN_BOT_TOKEN', 'FINANCE_BOT_TOKEN',
                'MAINTENANCE_BOT_TOKEN', 'MANAGEMENT_BOT_TOKEN',
                'ORDER_TRACK_BOT_TOKEN', 'DELIVERY_BOT_TOKEN',
                'SYSTEM_ADMIN_ID', 'FIREBASE_DATABASE_URL'
            ]
            
            missing_vars = []
            for var in required_vars:
                if var not in content:
                    missing_vars.append(var)
            
            if missing_vars:
                print(f"❌ Missing variables in .env.example: {', '.join(missing_vars)}")
                return False
            else:
                print("✅ All required variables found in .env.example")
                return True
    except Exception as e:
        print(f"❌ Error reading .env.example: {e}")
        return False

def check_railway_config():
    """Check Railway configuration files"""
    files_ok = True
    
    # Check railway.json
    if check_file_exists('railway.json', 'Railway config'):
        try:
            with open('railway.json', 'r') as f:
                config = json.load(f)
                if 'deploy' in config and 'startCommand' in config['deploy']:
                    print(f"✅ Start command: {config['deploy']['startCommand']}")
                else:
                    print("❌ No start command found in railway.json")
                    files_ok = False
        except Exception as e:
            print(f"❌ Error reading railway.json: {e}")
            files_ok = False
    else:
        files_ok = False
    
    # Check other files
    files_ok &= check_file_exists('nixpacks.toml', 'Nixpacks config')
    files_ok &= check_file_exists('Procfile', 'Procfile')
    files_ok &= check_file_exists('runtime.txt', 'Python runtime')
    
    return files_ok

def check_requirements():
    """Check requirements.txt"""
    if not check_file_exists('requirements.txt', 'Requirements file'):
        return False
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read()
            required_packages = ['pyTelegramBotAPI', 'python-dotenv', 'firebase-admin']
            
            missing_packages = []
            for package in required_packages:
                if package not in content:
                    missing_packages.append(package)
            
            if missing_packages:
                print(f"❌ Missing packages in requirements.txt: {', '.join(missing_packages)}")
                return False
            else:
                print("✅ All required packages found in requirements.txt")
                return True
    except Exception as e:
        print(f"❌ Error reading requirements.txt: {e}")
        return False

def check_main_structure():
    """Check main application structure"""
    files_ok = True
    
    files_ok &= check_file_exists('main.py', 'Main application')
    files_ok &= check_file_exists('src/config.py', 'Configuration module')
    files_ok &= check_file_exists('src/bot_instance.py', 'Bot instance module')
    files_ok &= check_file_exists('src/utils/health_server.py', 'Health server module')
    
    return files_ok

def main():
    """Main test function"""
    print("🧪 Testing Wiz Aroma Deployment Readiness")
    print("=" * 50)
    
    all_checks_passed = True
    
    print("\n📁 Checking file structure...")
    all_checks_passed &= check_main_structure()
    
    print("\n📦 Checking requirements...")
    all_checks_passed &= check_requirements()
    
    print("\n🌍 Checking environment configuration...")
    all_checks_passed &= check_env_example()
    
    print("\n🚂 Checking Railway configuration...")
    all_checks_passed &= check_railway_config()
    
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 All checks passed! Ready for Railway deployment.")
        print("\n📋 Next steps:")
        print("1. Push code to GitHub")
        print("2. Create Railway project from GitHub repo")
        print("3. Set environment variables in Railway dashboard")
        print("4. Deploy and monitor logs")
        return 0
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
