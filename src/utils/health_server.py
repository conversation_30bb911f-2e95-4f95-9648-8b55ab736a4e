"""
Health check server for Railway deployment monitoring.
Provides a simple HTTP endpoint for Railway to check if the application is running.
"""

import threading
import time
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from src.utils.logging_utils import get_logger

logger = get_logger()

class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for health checks"""
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/health':
            self.send_health_response()
        elif self.path == '/status':
            self.send_status_response()
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def send_health_response(self):
        """Send health check response"""
        try:
            # Simple health check - if we can respond, we're healthy
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            health_data = {
                'status': 'healthy',
                'timestamp': time.time(),
                'service': 'wiz-aroma-bot'
            }
            
            self.wfile.write(json.dumps(health_data).encode())
        except Exception as e:
            logger.error(f"Health check error: {e}")
            self.send_response(500)
            self.end_headers()
            self.wfile.write(b'Internal Server Error')
    
    def send_status_response(self):
        """Send detailed status response"""
        try:
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            status_data = {
                'status': 'running',
                'service': 'Wiz Aroma Food Delivery Bot',
                'version': '2.1',
                'timestamp': time.time(),
                'uptime': time.time() - start_time if 'start_time' in globals() else 0
            }
            
            self.wfile.write(json.dumps(status_data).encode())
        except Exception as e:
            logger.error(f"Status check error: {e}")
            self.send_response(500)
            self.end_headers()
            self.wfile.write(b'Internal Server Error')
    
    def log_message(self, format, *args):
        """Override to use our logger instead of printing to stderr"""
        logger.debug(f"Health server: {format % args}")

# Global variable to track start time
start_time = time.time()

def start_health_server(port=8000):
    """Start the health check server in a separate thread"""
    global start_time
    start_time = time.time()
    
    try:
        server = HTTPServer(('0.0.0.0', port), HealthCheckHandler)
        logger.info(f"Health check server starting on port {port}")
        
        # Run server in a separate thread so it doesn't block the main application
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        
        logger.info(f"Health check server running at http://0.0.0.0:{port}/health")
        return server
        
    except Exception as e:
        logger.error(f"Failed to start health check server: {e}")
        return None

def stop_health_server(server):
    """Stop the health check server"""
    if server:
        try:
            server.shutdown()
            logger.info("Health check server stopped")
        except Exception as e:
            logger.error(f"Error stopping health check server: {e}")
