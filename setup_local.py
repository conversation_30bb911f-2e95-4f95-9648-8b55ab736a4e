#!/usr/bin/env python3
"""
Local development setup script for Wiz Aroma Food Delivery System
"""

import os
import sys
import subprocess
import shutil

def run_command(command, description):
    """Run a command and report status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def setup_environment():
    """Set up local development environment"""
    print("🚀 Setting up Wiz Aroma local development environment")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("📝 Creating .env file from template...")
            shutil.copy('.env.example', '.env')
            print("✅ .env file created")
            print("⚠️  Please edit .env file and add your actual bot tokens and configuration")
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
    
    # Test deployment readiness
    if not run_command("python test_deployment.py", "Testing deployment readiness"):
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Local development environment setup complete!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your actual bot tokens")
    print("2. Set up Firebase project and add credentials")
    print("3. Run: python main.py --test (for testing without Telegram)")
    print("4. Run: python main.py --bot all (for full system)")
    print("\n🚂 For Railway deployment:")
    print("1. Push code to GitHub")
    print("2. Follow RAILWAY_DEPLOYMENT.md guide")
    
    return True

def main():
    """Main setup function"""
    try:
        return 0 if setup_environment() else 1
    except KeyboardInterrupt:
        print("\n🛑 Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Setup failed with error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
