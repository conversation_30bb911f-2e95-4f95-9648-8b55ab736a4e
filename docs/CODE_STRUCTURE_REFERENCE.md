# Wiz Aroma Food Delivery System - Code Structure Reference

## 📋 Table of Contents
- [Project Directory Structure](#project-directory-structure)
- [Core Application Files](#core-application-files)
- [Source Code Organization](#source-code-organization)
- [Handler Modules](#handler-modules)
- [Utility Modules](#utility-modules)
- [Configuration Files](#configuration-files)
- [Documentation](#documentation)

## 📁 Project Directory Structure

```
WA-ASTU/
├── 📄 Core Application Files
│   ├── main.py                     # Application entry point
│   ├── requirements.txt            # Python dependencies
│   ├── .env                        # Environment configuration (production)
│   ├── .env.example               # Environment template
│   ├── Dockerfile                 # Container configuration
│   ├── docker-compose.yml         # Multi-container orchestration
│   └── docker-compose.railway.yml # Railway deployment config
│
├── 📂 src/ (Source Code)
│   ├── __init__.py                # Package initialization
│   ├── config.py                  # Application configuration
│   ├── bot_instance.py            # Bot instance management
│   ├── data_models.py             # Data structure definitions
│   ├── data_storage.py            # Data persistence layer
│   ├── firebase_db.py             # Firebase integration
│   │
│   ├── 🤖 bots/                   # Bot-specific implementations
│   │   ├── delivery_bot.py        # Delivery personnel bot
│   │   ├── management_bot.py      # Management analytics bot
│   │   └── order_track_bot.py     # Order tracking bot
│   │
│   ├── 📊 data/                   # Data management
│   │   ├── __init__.py
│   │   └── menus.py               # Menu data structures
│   │
│   ├── 🎛️ handlers/               # Request handlers
│   │   ├── __init__.py
│   │   ├── admin_handlers.py      # Administrative functions
│   │   ├── delivery_personnel_handlers.py # Delivery coordination
│   │   ├── favorite_orders_handlers.py    # Favorite orders management
│   │   ├── location_handlers.py   # Location and delivery areas
│   │   ├── main_handlers.py       # Core user interactions
│   │   ├── maintenance_handlers.py # System maintenance
│   │   ├── order_handlers.py      # Order lifecycle management
│   │   ├── payment_handlers.py    # Payment processing
│   │   └── user_profile_handlers.py # User profile management
│   │
│   └── 🔧 utils/                  # Utility modules
│       ├── __init__.py
│       ├── access_control.py      # Authorization and permissions
│       ├── analytics_counter_system.py # Analytics and metrics
│       ├── auto_cleanup_service.py # Automatic data cleanup
│       ├── data_consistency.py    # Data integrity checks
│       ├── data_management_patterns.py # Data management patterns
│       ├── data_sync.py           # Data synchronization
│       ├── delivery_personnel_utils.py # Delivery personnel utilities
│       ├── earnings_utils.py      # Financial calculations
│       ├── error_handling.py      # Error handling and logging
│       ├── favorite_orders_sync.py # Favorite orders synchronization
│       ├── financial_calculations.py # Financial operations
│       ├── firebase_business_data.py # Firebase business logic
│       ├── handler_registration.py # Handler registration system
│       ├── helpers.py             # General helper functions
│       ├── keyboards.py           # Telegram keyboard layouts
│       ├── logging_utils.py       # Logging configuration
│       ├── temp_data_manager.py   # Temporary data management
│       ├── text_utils.py          # Text processing utilities
│       ├── time_based_reset_utils.py # Time-based operations
│       ├── time_utils.py          # Time and date utilities
│       └── validation.py          # Input validation
│
├── 📚 docs/                       # Documentation
│   ├── ACADEMIC_PROPOSAL.md       # Academic project proposal
│   ├── BOT_STARTUP_GUIDE.md       # Bot startup instructions
│   ├── CHANGELOG.md               # Version history
│   ├── COMPREHENSIVE_SYSTEM_ARCHITECTURE.md # System architecture
│   ├── DEPLOYMENT.md              # Deployment guide
│   ├── PROJECT_STRUCTURE.md       # Project organization
│   ├── README.md                  # Documentation overview
│   ├── RELEASE_NOTES_V2.0.md      # Version 2.0 release notes
│   ├── RELEASE_NOTES_V2.1.md      # Version 2.1 release notes
│   └── SYSTEM_ARCHITECTURE.md     # Original architecture doc
│
├── 🎨 assets/                     # Project assets
│   └── Logo/                      # Brand logos and images
│       ├── Wiz-01.png through Wiz-07.png
│       └── Wiz-10.png
│
├── 📜 scripts/                    # Utility scripts
│   └── verify_clean_architecture.py # Architecture verification
│
├── 📊 data_files/                 # Data storage (empty - Firebase-only)
│
└── 📄 Project Files
    ├── .gitignore                 # Git ignore rules
    ├── .dockerignore              # Docker ignore rules
    ├── LICENSE                    # Project license
    └── README.md                  # Main project documentation
```

## 📄 Core Application Files

### main.py
- **Purpose**: Application entry point and bot orchestration
- **Functions**: Initialize all bots, start polling, handle graceful shutdown
- **Key Components**: Bot instance creation, error handling, logging setup

### config.py
- **Purpose**: Centralized configuration management
- **Functions**: Environment variable loading, bot token management, Firebase config
- **Key Components**: Configuration validation, default values, environment detection

### bot_instance.py
- **Purpose**: Bot instance management and initialization
- **Functions**: Create bot instances, configure handlers, manage bot lifecycle
- **Key Components**: Bot factory pattern, handler registration, error handling

## 🏗️ Source Code Organization

### Data Layer (`src/data_*.py`)

#### data_models.py
- **Purpose**: Define data structures and models
- **Key Classes**: Order, User, Restaurant, MenuItem, DeliveryLocation
- **Functions**: Data validation, serialization, model relationships

#### data_storage.py
- **Purpose**: Data persistence and storage operations
- **Functions**: CRUD operations, data synchronization, cache management
- **Key Components**: Firebase integration, local caching, data consistency

#### firebase_db.py
- **Purpose**: Firebase Realtime Database integration
- **Functions**: Database connections, real-time listeners, data operations
- **Key Components**: Authentication, error handling, connection management

### Bot Layer (`src/bots/`)

#### delivery_bot.py
- **Purpose**: Delivery personnel coordination and communication
- **Functions**: Order assignment, delivery tracking, personnel management
- **Key Features**: Broadcast messaging, status updates, location tracking

#### management_bot.py
- **Purpose**: Management analytics and reporting
- **Functions**: Performance metrics, personnel analytics, financial reports
- **Key Features**: Data visualization, report generation, dashboard functions

#### order_track_bot.py
- **Purpose**: Internal order monitoring and tracking
- **Functions**: Order status monitoring, internal notifications, system alerts
- **Key Features**: Real-time tracking, automated notifications, status updates

## 🎛️ Handler Modules

### Core Handlers

#### main_handlers.py
- **Purpose**: Primary user interactions and core functionality
- **Functions**: Order initiation, menu browsing, basic commands
- **Key Features**: User onboarding, menu navigation, order flow

#### order_handlers.py
- **Purpose**: Complete order lifecycle management
- **Functions**: Order creation, modification, status tracking, completion
- **Key Features**: Order validation, status updates, customer notifications

#### payment_handlers.py
- **Purpose**: Payment processing and verification
- **Functions**: Payment method selection, receipt processing, verification
- **Key Features**: Multiple payment methods, receipt validation, confirmation

#### admin_handlers.py
- **Purpose**: Administrative functions and oversight
- **Functions**: Order approval, system management, user administration
- **Key Features**: Order review, system controls, administrative tools

### Specialized Handlers

#### maintenance_handlers.py
- **Purpose**: System maintenance and configuration
- **Functions**: Menu updates, system settings, data management
- **Key Features**: Real-time updates, configuration management, data integrity

#### delivery_personnel_handlers.py
- **Purpose**: Delivery coordination and management
- **Functions**: Personnel assignment, delivery tracking, communication
- **Key Features**: Assignment algorithms, tracking systems, communication tools

#### location_handlers.py
- **Purpose**: Location and delivery area management
- **Functions**: Area configuration, delivery zones, location validation
- **Key Features**: Geographic management, delivery optimization, area mapping

## 🔧 Utility Modules

### Core Utilities

#### validation.py
- **Purpose**: Input validation and data integrity
- **Functions**: Data validation, format checking, security validation
- **Key Features**: Comprehensive validation rules, error handling, security checks

#### error_handling.py
- **Purpose**: Error management and logging
- **Functions**: Exception handling, error logging, user-friendly error messages
- **Key Features**: Centralized error handling, logging integration, user experience

#### keyboards.py
- **Purpose**: Telegram keyboard layouts and UI components
- **Functions**: Dynamic keyboard generation, UI state management
- **Key Features**: Responsive layouts, context-aware keyboards, user experience

### Data Management Utilities

#### firebase_business_data.py
- **Purpose**: Firebase business logic and data operations
- **Functions**: Business-specific data operations, complex queries, data relationships
- **Key Features**: Business logic encapsulation, data consistency, performance optimization

#### auto_cleanup_service.py
- **Purpose**: Automatic data lifecycle management
- **Functions**: Temporary data cleanup, session management, data archival
- **Key Features**: Automated cleanup, configurable retention, performance optimization

#### temp_data_manager.py
- **Purpose**: Temporary data management and caching
- **Functions**: Session data, temporary storage, cache management
- **Key Features**: Memory management, data expiration, performance optimization

### Business Logic Utilities

#### financial_calculations.py
- **Purpose**: Financial operations and calculations
- **Functions**: Price calculations, tax computation, discount application
- **Key Features**: Accurate calculations, currency handling, business rules

#### time_utils.py
- **Purpose**: Time and date operations
- **Functions**: Business hours, delivery scheduling, time zone handling
- **Key Features**: Ethiopian time zone, business logic, scheduling algorithms

#### analytics_counter_system.py
- **Purpose**: Analytics and metrics collection
- **Functions**: Usage tracking, performance metrics, business intelligence
- **Key Features**: Real-time analytics, data aggregation, reporting

## 📋 Configuration Files

### Environment Configuration
- **.env**: Production environment variables
- **.env.example**: Environment template with documentation
- **docker-compose.yml**: Local development container configuration
- **docker-compose.railway.yml**: Railway deployment configuration

### Build Configuration
- **Dockerfile**: Container build instructions
- **requirements.txt**: Python dependency specifications
- **.gitignore**: Git version control exclusions
- **.dockerignore**: Docker build exclusions

## 📚 Documentation

### Technical Documentation
- **COMPREHENSIVE_SYSTEM_ARCHITECTURE.md**: Complete system architecture
- **CODE_STRUCTURE_REFERENCE.md**: This document - code organization
- **PROJECT_STRUCTURE.md**: Project organization and standards

### Operational Documentation
- **BOT_STARTUP_GUIDE.md**: Bot startup and operation instructions
- **DEPLOYMENT.md**: Deployment procedures and requirements
- **CHANGELOG.md**: Version history and changes

### Release Documentation
- **RELEASE_NOTES_V2.0.md**: Version 2.0 features and changes
- **RELEASE_NOTES_V2.1.md**: Version 2.1 features and changes

---

*This document provides a comprehensive reference for the Wiz Aroma Food Delivery System codebase structure. For architectural details, see the System Architecture documentation.*
