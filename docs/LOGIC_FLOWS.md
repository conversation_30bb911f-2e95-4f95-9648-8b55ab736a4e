# Wiz Aroma Food Delivery System - Logic Flows

## 📋 Table of Contents
- [Order Placement Flow](#order-placement-flow)
- [Payment Processing Flow](#payment-processing-flow)
- [Admin Order Management Flow](#admin-order-management-flow)
- [Delivery Assignment Flow](#delivery-assignment-flow)
- [Firebase Data Synchronization](#firebase-data-synchronization)
- [Bot Startup and Initialization](#bot-startup-and-initialization)
- [Error Handling and Recovery](#error-handling-and-recovery)

## 🛍️ Order Placement Flow

### Complete Customer Order Journey

```mermaid
flowchart TD
    A[Customer starts /start] --> B[Show Welcome Message]
    B --> C[Display Main Menu]
    C --> D{Customer Choice}
    
    D -->|Order Food| E[Show Restaurant Areas]
    D -->|View Profile| F[Show User Profile]
    D -->|Help| G[Show Help Information]
    
    E --> H[Select Area]
    H --> I[Show Restaurants in Area]
    I --> J[Select Restaurant]
    J --> K[Show Restaurant Menu]
    K --> L[Select Menu Items]
    L --> M{Add More Items?}
    
    M -->|Yes| K
    M -->|No| N[Show Order Summary]
    
    N --> O[Confirm Order Details]
    O --> P[Request Delivery Location]
    P --> Q[Select Payment Method]
    Q --> R[Process Payment]
    R --> S[Submit Order for Review]
    S --> T[Notify Admin Bot]
    T --> U[Wait for Admin Approval]
    U --> V[Order Complete]
```

### Detailed Order State Management

```mermaid
stateDiagram-v2
    [*] --> IDLE
    IDLE --> BROWSING_AREAS : /start or order command
    BROWSING_AREAS --> BROWSING_RESTAURANTS : area selected
    BROWSING_RESTAURANTS --> BROWSING_MENU : restaurant selected
    BROWSING_MENU --> ADDING_ITEMS : menu item selected
    ADDING_ITEMS --> ADDING_ITEMS : add more items
    ADDING_ITEMS --> REVIEWING_ORDER : done adding items
    REVIEWING_ORDER --> SELECTING_LOCATION : order confirmed
    SELECTING_LOCATION --> AWAITING_PAYMENT_METHOD : location selected
    AWAITING_PAYMENT_METHOD --> AWAITING_RECEIPT : payment method selected
    AWAITING_RECEIPT --> PENDING_ADMIN_REVIEW : receipt uploaded
    PENDING_ADMIN_REVIEW --> ORDER_APPROVED : admin approves
    PENDING_ADMIN_REVIEW --> ORDER_REJECTED : admin rejects
    ORDER_APPROVED --> ORDER_COMPLETED : delivery completed
    ORDER_REJECTED --> IDLE : order cancelled
    ORDER_COMPLETED --> IDLE : process complete
```

## 💳 Payment Processing Flow

### Payment Method Selection and Verification

```mermaid
sequenceDiagram
    participant Customer
    participant UserBot
    participant PaymentHandler
    participant Firebase
    participant AdminBot
    participant Admin
    
    Customer->>UserBot: Select Payment Method
    UserBot->>PaymentHandler: Process Payment Selection
    PaymentHandler->>UserBot: Show Payment Instructions
    UserBot->>Customer: Display Payment Details
    
    Note over Customer: Customer makes payment<br/>using provided details
    
    Customer->>UserBot: Upload Payment Receipt
    UserBot->>PaymentHandler: Validate Receipt Format
    PaymentHandler->>Firebase: Store Receipt Data
    PaymentHandler->>UserBot: Receipt Stored Successfully
    
    UserBot->>AdminBot: Notify Payment Received
    AdminBot->>Admin: Request Payment Verification
    Admin->>AdminBot: Verify/Reject Payment
    AdminBot->>Firebase: Update Payment Status
    AdminBot->>UserBot: Payment Status Update
    UserBot->>Customer: Payment Confirmation
```

### Payment Methods Supported

```mermaid
graph LR
    A[Payment Selection] --> B[Telebirr Mobile]
    A --> C[CBE Bank Transfer]
    A --> D[BOA Bank Transfer]
    
    B --> E[Show Telebirr Details]
    C --> F[Show CBE Account Info]
    D --> G[Show BOA Account Info]
    
    E --> H[Upload Receipt]
    F --> H
    G --> H
    
    H --> I[Admin Verification]
    I --> J[Payment Confirmed]
```

## 👨‍💼 Admin Order Management Flow

### Administrative Order Review Process

```mermaid
flowchart TD
    A[New Order Notification] --> B[Admin Reviews Order]
    B --> C{Order Valid?}
    
    C -->|Yes| D[Check Payment Receipt]
    C -->|No| E[Reject Order]
    
    D --> F{Payment Verified?}
    F -->|Yes| G[Approve Order]
    F -->|No| H[Request Payment Clarification]
    
    G --> I[Update Order Status]
    I --> J[Notify Customer]
    J --> K[Assign to Delivery]
    K --> L[Track Delivery Progress]
    L --> M[Mark Order Complete]
    
    E --> N[Notify Customer of Rejection]
    H --> O[Request Additional Payment Info]
    O --> D
```

### Admin Dashboard Functions

```mermaid
graph TB
    subgraph "Admin Bot Functions"
        A[Pending Orders Review]
        B[Order Status Management]
        C[Customer Communication]
        D[Payment Verification]
        E[Delivery Coordination]
        F[System Monitoring]
    end
    
    A --> A1[View Order Details]
    A --> A2[Approve/Reject Orders]
    
    B --> B1[Update Order Status]
    B --> B2[Track Order Progress]
    
    C --> C1[Send Customer Messages]
    C --> C2[Handle Customer Queries]
    
    D --> D1[Verify Payment Receipts]
    D --> D2[Confirm Payment Methods]
    
    E --> E1[Assign Delivery Personnel]
    E --> E2[Track Delivery Status]
    
    F --> F1[Monitor System Health]
    F --> F2[View Analytics]
```

## 🚚 Delivery Assignment Flow

### Delivery Personnel Coordination

```mermaid
sequenceDiagram
    participant AdminBot
    participant DeliveryBot
    participant DeliveryPersonnel
    participant Firebase
    participant Customer
    
    AdminBot->>DeliveryBot: Order Ready for Delivery
    DeliveryBot->>Firebase: Get Available Personnel
    DeliveryBot->>DeliveryPersonnel: Broadcast Order Assignment
    DeliveryPersonnel->>DeliveryBot: Accept Assignment
    DeliveryBot->>Firebase: Update Assignment Status
    DeliveryBot->>AdminBot: Delivery Assigned
    
    Note over DeliveryPersonnel: Delivery in progress
    
    DeliveryPersonnel->>DeliveryBot: Update Delivery Status
    DeliveryBot->>Firebase: Store Status Update
    DeliveryBot->>AdminBot: Status Update
    AdminBot->>Customer: Delivery Progress Update
    
    DeliveryPersonnel->>DeliveryBot: Delivery Completed
    DeliveryBot->>Firebase: Mark Order Complete
    DeliveryBot->>AdminBot: Delivery Confirmed
    AdminBot->>Customer: Order Delivered
```

## 🔥 Firebase Data Synchronization

### Real-time Data Flow

```mermaid
graph TB
    subgraph "Application Layer"
        A[User Action]
        B[Handler Processing]
        C[Data Validation]
    end
    
    subgraph "Data Layer"
        D[Local Data Update]
        E[Firebase Write Operation]
        F[Firebase Real-time Listener]
    end
    
    subgraph "Synchronization Layer"
        G[All Connected Bots]
        H[Real-time Updates]
        I[Data Consistency Check]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> A
```

### Data Consistency Management

```mermaid
flowchart LR
    A[Data Write Request] --> B[Validation Layer]
    B --> C{Data Valid?}
    
    C -->|Yes| D[Firebase Write]
    C -->|No| E[Return Error]
    
    D --> F[Write Success?]
    F -->|Yes| G[Update Local Cache]
    F -->|No| H[Retry Logic]
    
    G --> I[Notify All Bots]
    H --> J{Retry Count < Max?}
    J -->|Yes| D
    J -->|No| K[Log Error & Alert]
    
    I --> L[Data Synchronized]
```

## 🚀 Bot Startup and Initialization

### System Startup Sequence

```mermaid
sequenceDiagram
    participant Main
    participant Config
    participant Firebase
    participant BotInstances
    participant Handlers
    
    Main->>Config: Load Environment Variables
    Config->>Main: Configuration Ready
    
    Main->>Firebase: Initialize Firebase Connection
    Firebase->>Main: Connection Established
    
    Main->>BotInstances: Create Bot Instances
    BotInstances->>Main: Bots Created
    
    Main->>Handlers: Register All Handlers
    Handlers->>Main: Handlers Registered
    
    Main->>BotInstances: Start Bot Polling
    BotInstances->>Main: All Bots Running
    
    Note over Main: System Ready for Requests
```

### Bot Instance Initialization

```mermaid
graph TD
    A[Application Start] --> B[Load Environment Variables]
    B --> C[Validate Configuration]
    C --> D[Initialize Firebase]
    D --> E[Create Bot Instances]
    
    E --> F[User Bot]
    E --> G[Admin Bot]
    E --> H[Finance Bot]
    E --> I[Maintenance Bot]
    E --> J[Management Bot]
    E --> K[Order Track Bot]
    E --> L[Delivery Bot]
    
    F --> M[Register Handlers]
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[Start Polling]
    N --> O[System Ready]
```

## ⚠️ Error Handling and Recovery

### Error Recovery Flow

```mermaid
flowchart TD
    A[Error Occurs] --> B[Error Handler Catches]
    B --> C{Error Type?}
    
    C -->|Network Error| D[Retry with Backoff]
    C -->|Validation Error| E[Return User-Friendly Message]
    C -->|Firebase Error| F[Check Connection & Retry]
    C -->|Bot API Error| G[Log Error & Continue]
    C -->|Critical Error| H[Alert Admin & Restart]
    
    D --> I{Retry Successful?}
    I -->|Yes| J[Continue Operation]
    I -->|No| K[Log Error & Alert]
    
    E --> L[User Notification]
    F --> M{Connection Restored?}
    M -->|Yes| J
    M -->|No| K
    
    G --> N[Continue with Fallback]
    H --> O[System Recovery]
```

### Graceful Degradation

```mermaid
graph LR
    A[Service Failure] --> B{Critical Service?}
    
    B -->|Yes| C[Switch to Backup]
    B -->|No| D[Continue with Reduced Functionality]
    
    C --> E[Notify Administrators]
    D --> F[Log Warning]
    
    E --> G[Monitor Recovery]
    F --> H[Continue Operation]
    
    G --> I{Service Restored?}
    I -->|Yes| J[Resume Full Functionality]
    I -->|No| K[Maintain Backup Mode]
```

---

*This document provides detailed logic flows for all major operations in the Wiz Aroma Food Delivery System. For architectural context, see the System Architecture documentation.*
