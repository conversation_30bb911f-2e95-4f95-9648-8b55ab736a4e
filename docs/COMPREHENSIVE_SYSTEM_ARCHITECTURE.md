# Wiz Aroma Food Delivery System - Comprehensive System Architecture

## 📋 Table of Contents
- [System Overview](#system-overview)
- [Architecture Diagram](#architecture-diagram)
- [Component Breakdown](#component-breakdown)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Security Architecture](#security-architecture)
- [Deployment Architecture](#deployment-architecture)

## 🏗️ System Overview

The Wiz Aroma Food Delivery System is a comprehensive multi-bot Telegram-based food delivery platform built with a microservices-inspired architecture. The system handles the complete food delivery workflow from order placement to delivery completion.

### Key Characteristics:
- **Multi-Bot Architecture**: 7 specialized bots for different functions
- **Firebase-First Data Storage**: All data stored in Firebase Realtime Database
- **Real-time Operations**: Live order tracking and status updates
- **Scalable Design**: Modular components for easy expansion
- **Production-Ready**: Containerized deployment with Docker

## 🎯 Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        UC[User Chat Interface]
        AC[Admin Chat Interface]
        DC[Delivery Chat Interface]
    end
    
    subgraph "Bot Layer"
        UB[User Bot<br/>Order Management]
        AB[Admin Bot<br/>Order Oversight]
        FB[Finance Bot<br/>Payment Verification]
        MB[Maintenance Bot<br/>System Config]
        MGB[Management Bot<br/>Analytics & Reports]
        OTB[Order Track Bot<br/>Internal Monitoring]
        DB[Delivery Bot<br/>Personnel Assignment]
    end
    
    subgraph "Business Logic Layer"
        OH[Order Handlers]
        PH[Payment Handlers]
        AH[Admin Handlers]
        MH[Maintenance Handlers]
        DH[Delivery Handlers]
    end
    
    subgraph "Data Management Layer"
        DS[Data Storage Layer]
        DM[Data Models]
        VS[Validation Services]
        CS[Cleanup Services]
    end
    
    subgraph "External Services"
        FB_DB[(Firebase Realtime DB)]
        TG[Telegram API]
        EMAIL[Email Service]
    end
    
    UC --> UB
    AC --> AB
    DC --> DB
    
    UB --> OH
    AB --> AH
    FB --> PH
    MB --> MH
    MGB --> AH
    OTB --> OH
    DB --> DH
    
    OH --> DS
    PH --> DS
    AH --> DS
    MH --> DS
    DH --> DS
    
    DS --> DM
    DS --> VS
    DS --> CS
    
    DS <--> FB_DB
    UB <--> TG
    AB <--> TG
    FB <--> TG
    MB <--> TG
    MGB <--> TG
    OTB <--> TG
    DB <--> TG
    
    DS --> EMAIL
```

## 🔧 Component Breakdown

### 1. Bot Layer Components

#### User Bot (`BOT_TOKEN`)
- **Purpose**: Customer-facing order management
- **Functions**: Order placement, menu browsing, payment processing
- **Access**: Public (all Telegram users)

#### Admin Bot (`ADMIN_BOT_TOKEN`)
- **Purpose**: Administrative oversight and order management
- **Functions**: Order approval, status updates, customer communication
- **Access**: Restricted to admin users

#### Finance Bot (`FINANCE_BOT_TOKEN`)
- **Purpose**: Payment verification and financial operations
- **Functions**: Payment confirmation, financial reporting
- **Access**: Restricted to finance personnel

#### Maintenance Bot (`MAINTENANCE_BOT_TOKEN`)
- **Purpose**: System configuration and data management
- **Functions**: Menu updates, area management, system settings
- **Access**: Restricted to system administrators

#### Management Bot (`MANAGEMENT_BOT_TOKEN`)
- **Purpose**: Analytics, personnel management, and reporting
- **Functions**: Performance analytics, delivery personnel management
- **Access**: Restricted to management team

#### Order Track Bot (`ORDER_TRACK_BOT_TOKEN`)
- **Purpose**: Internal order monitoring and tracking
- **Functions**: Order status tracking, internal notifications
- **Access**: Restricted to authorized personnel

#### Delivery Bot (`DELIVERY_BOT_TOKEN`)
- **Purpose**: Delivery personnel coordination
- **Functions**: Order assignment, delivery tracking, personnel communication
- **Access**: Restricted to delivery personnel and coordinators

### 2. Business Logic Layer

#### Handler Modules
- **Order Handlers**: Order lifecycle management
- **Payment Handlers**: Payment processing and verification
- **Admin Handlers**: Administrative functions and oversight
- **Maintenance Handlers**: System configuration and maintenance
- **Delivery Handlers**: Delivery coordination and tracking

### 3. Data Management Layer

#### Core Components
- **Data Storage Layer**: Firebase integration and data persistence
- **Data Models**: Structured data representations
- **Validation Services**: Data integrity and validation
- **Cleanup Services**: Automatic data lifecycle management

## 🔄 Data Flow

### Order Placement Flow
```mermaid
sequenceDiagram
    participant Customer
    participant UserBot
    participant Firebase
    participant AdminBot
    participant Admin
    
    Customer->>UserBot: Start Order
    UserBot->>Firebase: Create Order Record
    UserBot->>Customer: Show Menu
    Customer->>UserBot: Select Items
    UserBot->>Firebase: Update Order Items
    UserBot->>Customer: Request Payment
    Customer->>UserBot: Submit Payment Receipt
    UserBot->>Firebase: Store Receipt
    UserBot->>AdminBot: Notify New Order
    AdminBot->>Admin: Order Review Request
    Admin->>AdminBot: Approve/Reject
    AdminBot->>Firebase: Update Order Status
    AdminBot->>UserBot: Status Update
    UserBot->>Customer: Order Confirmation
```

### Data Synchronization Flow
```mermaid
graph LR
    A[User Action] --> B[Handler Processing]
    B --> C[Data Validation]
    C --> D[Firebase Update]
    D --> E[Real-time Sync]
    E --> F[All Connected Bots]
    F --> G[User Notification]
```

## 💻 Technology Stack

### Core Technologies
- **Language**: Python 3.9+
- **Bot Framework**: pyTelegramBotAPI
- **Database**: Firebase Realtime Database
- **Authentication**: Firebase Service Account
- **Configuration**: python-dotenv

### Infrastructure
- **Containerization**: Docker
- **Orchestration**: Docker Compose
- **Deployment**: Railway/Cloud platforms
- **Monitoring**: Built-in logging system

### Dependencies
```
pyTelegramBotAPI>=4.12.0
python-dotenv>=1.0.0
requests>=2.31.0
psutil>=5.9.0
firebase-admin>=6.2.0
```

## 🔒 Security Architecture

### Authentication & Authorization
- **Bot Token Security**: Individual tokens for each bot
- **User Access Control**: Telegram ID-based authorization
- **Role-Based Access**: Different permission levels per bot
- **Firebase Security**: Service account authentication

### Data Protection
- **Environment Variables**: Sensitive data in .env files
- **Firebase Rules**: Database-level security rules
- **Input Validation**: Comprehensive data validation
- **Error Handling**: Secure error messages

### Access Control Matrix
| Bot Type | Public Access | Admin Access | Finance Access | Delivery Access |
|----------|---------------|--------------|----------------|-----------------|
| User Bot | ✅ | ✅ | ❌ | ❌ |
| Admin Bot | ❌ | ✅ | ❌ | ❌ |
| Finance Bot | ❌ | ✅ | ✅ | ❌ |
| Maintenance Bot | ❌ | ✅ | ❌ | ❌ |
| Management Bot | ❌ | ✅ | ✅ | ✅ |
| Order Track Bot | ❌ | ✅ | ❌ | ❌ |
| Delivery Bot | ❌ | ✅ | ❌ | ✅ |

## 🚀 Deployment Architecture

### Container Structure
```
wiz-aroma-delivery-bot/
├── Application Container
│   ├── Python Runtime
│   ├── Bot Instances
│   ├── Business Logic
│   └── Data Management
├── Volume Mounts
│   └── ./data_files:/app/data_files
└── Environment Variables
    └── .env configuration
```

### Network Architecture
```mermaid
graph TB
    subgraph "External Network"
        TG_API[Telegram API]
        FB_API[Firebase API]
    end
    
    subgraph "Container Network"
        APP[Application Container]
        HEALTH[Health Check]
    end
    
    subgraph "Host Network"
        PORTS[Port 8000]
        VOLUMES[Data Volumes]
    end
    
    TG_API <--> APP
    FB_API <--> APP
    APP --> HEALTH
    APP --> PORTS
    APP --> VOLUMES
```

### Scaling Considerations
- **Horizontal Scaling**: Multiple container instances
- **Load Balancing**: Telegram webhook distribution
- **Database Scaling**: Firebase auto-scaling
- **Resource Management**: Container resource limits

---

*This document provides a comprehensive overview of the Wiz Aroma Food Delivery System architecture. For implementation details, see the Code Structure Reference and Logic Flows documentation.*
