# Wiz Aroma Food Delivery System - Project Standards & Best Practices

## 📋 Table of Contents
- [Project Structure Standards](#project-structure-standards)
- [Development Workflow](#development-workflow)
- [Code Quality Standards](#code-quality-standards)
- [Environment Management](#environment-management)
- [Testing Strategy](#testing-strategy)
- [Deployment Process](#deployment-process)
- [Monitoring and Logging](#monitoring-and-logging)
- [Security Best Practices](#security-best-practices)

## 🏗️ Project Structure Standards

### Essential Production Files Checklist

#### ✅ Core Application Files
- [ ] `main.py` - Application entry point
- [ ] `requirements.txt` - Python dependencies
- [ ] `.env` - Production environment variables
- [ ] `.env.example` - Environment template with documentation
- [ ] `Dockerfile` - Container configuration
- [ ] `docker-compose.yml` - Multi-container orchestration

#### ✅ Source Code Organization
- [ ] `src/` - Main source code directory
- [ ] `src/config.py` - Centralized configuration
- [ ] `src/bot_instance.py` - Bot management
- [ ] `src/handlers/` - Request handlers by functionality
- [ ] `src/utils/` - Utility modules and helpers
- [ ] `src/data_models.py` - Data structure definitions

#### ✅ Documentation Requirements
- [ ] `README.md` - Project overview and setup
- [ ] `docs/SYSTEM_ARCHITECTURE.md` - Technical architecture
- [ ] `docs/CODE_STRUCTURE_REFERENCE.md` - Code organization
- [ ] `docs/LOGIC_FLOWS.md` - Business logic flows
- [ ] `docs/DEPLOYMENT.md` - Deployment procedures

#### ✅ Configuration Files
- [ ] `.gitignore` - Version control exclusions
- [ ] `.dockerignore` - Docker build exclusions
- [ ] `LICENSE` - Project license

### Recommended Directory Structure

```
project-root/
├── 📄 Application Core
│   ├── main.py
│   ├── requirements.txt
│   ├── .env (production)
│   ├── .env.example
│   ├── Dockerfile
│   └── docker-compose.yml
│
├── 📂 src/ (Source Code)
│   ├── __init__.py
│   ├── config.py
│   ├── bot_instance.py
│   ├── data_models.py
│   ├── data_storage.py
│   ├── firebase_db.py
│   │
│   ├── handlers/
│   │   ├── __init__.py
│   │   ├── main_handlers.py
│   │   ├── order_handlers.py
│   │   ├── payment_handlers.py
│   │   ├── admin_handlers.py
│   │   └── [feature]_handlers.py
│   │
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── validation.py
│   │   ├── error_handling.py
│   │   ├── keyboards.py
│   │   └── [utility]_utils.py
│   │
│   └── bots/ (if multi-bot)
│       ├── [bot_name]_bot.py
│       └── ...
│
├── 📚 docs/
│   ├── README.md
│   ├── SYSTEM_ARCHITECTURE.md
│   ├── CODE_STRUCTURE_REFERENCE.md
│   ├── LOGIC_FLOWS.md
│   ├── PROJECT_STANDARDS.md
│   └── DEPLOYMENT.md
│
├── 🧪 tests/ (optional)
│   ├── test_handlers.py
│   ├── test_utils.py
│   └── test_integration.py
│
└── 📄 Project Files
    ├── .gitignore
    ├── .dockerignore
    ├── LICENSE
    └── README.md
```

## 🔄 Development Workflow

### Git Workflow Standards

#### Branch Naming Convention
- `main` - Production-ready code
- `develop` - Development integration branch
- `feature/[feature-name]` - New features
- `bugfix/[bug-description]` - Bug fixes
- `hotfix/[critical-fix]` - Critical production fixes

#### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

**Examples**:
```
feat(orders): add payment receipt validation
fix(firebase): resolve connection timeout issues
docs(api): update handler documentation
```

### Code Review Process

#### Pre-Commit Checklist
- [ ] Code follows project style guidelines
- [ ] All functions have docstrings
- [ ] Error handling is implemented
- [ ] No hardcoded credentials or sensitive data
- [ ] Tests pass (if applicable)
- [ ] Documentation is updated

#### Review Criteria
- **Functionality**: Does the code work as intended?
- **Readability**: Is the code easy to understand?
- **Performance**: Are there any performance concerns?
- **Security**: Are there any security vulnerabilities?
- **Maintainability**: Is the code easy to maintain and extend?

## 💻 Code Quality Standards

### Python Coding Standards

#### Naming Conventions
```python
# Variables and functions: snake_case
user_id = 12345
def process_order():
    pass

# Classes: PascalCase
class OrderHandler:
    pass

# Constants: UPPER_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TIMEOUT = 30

# Private methods: _leading_underscore
def _internal_helper():
    pass
```

#### Function Documentation
```python
def process_payment(user_id: int, amount: float, method: str) -> bool:
    """
    Process payment for a user order.
    
    Args:
        user_id (int): Telegram user ID
        amount (float): Payment amount in ETB
        method (str): Payment method ('telebirr', 'cbe', 'boa')
    
    Returns:
        bool: True if payment processed successfully, False otherwise
    
    Raises:
        ValueError: If amount is negative or method is invalid
        ConnectionError: If Firebase connection fails
    """
    pass
```

#### Error Handling Pattern
```python
import logging
from src.utils.error_handling import handle_exceptions

logger = logging.getLogger(__name__)

@handle_exceptions(error_message="Failed to process order")
def process_order(order_data):
    """Process order with comprehensive error handling."""
    try:
        # Main logic here
        result = perform_operation(order_data)
        logger.info(f"Order processed successfully: {result}")
        return result
    
    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        raise
    
    except Exception as e:
        logger.error(f"Unexpected error processing order: {e}")
        raise
```

### Code Organization Patterns

#### Handler Registration Pattern
```python
from src.utils.handler_registration import register_handler

@register_handler("user", commands=["start"])
def start_command(message):
    """Handle /start command."""
    pass

@register_handler("admin", callback_query=True)
def admin_callback(call):
    """Handle admin callback queries."""
    pass
```

#### Data Validation Pattern
```python
from src.utils.validation import validate_input

def create_order(user_id, items):
    """Create new order with validation."""
    # Validate inputs
    validate_input(user_id, 'user_id', required=True, type=int)
    validate_input(items, 'items', required=True, type=list, min_length=1)
    
    # Process order
    order = Order(user_id=user_id, items=items)
    return order.save()
```

## 🌍 Environment Management

### Environment Variable Standards

#### Variable Naming Convention
```bash
# Bot tokens: [BOT_TYPE]_BOT_TOKEN
BOT_TOKEN="main_bot_token"
ADMIN_BOT_TOKEN="admin_bot_token"

# Authorization: [SCOPE]_AUTHORIZED_IDS
ADMIN_AUTHORIZED_IDS="[123456789]"
DELIVERY_BOT_AUTHORIZED_IDS="[987654321]"

# External services: [SERVICE]_[PROPERTY]
FIREBASE_DATABASE_URL="https://project.firebaseio.com"
FIREBASE_CREDENTIALS="{...}"

# Application config: [FEATURE]_[SETTING]
LOG_LEVEL="INFO"
TEST_MODE="False"
```

#### Environment File Structure
```bash
# ===============================================================================
# Section Header with Clear Description
# ===============================================================================
# Detailed explanation of the section purpose and usage

# Variable with inline comment
VARIABLE_NAME="value"  # Purpose and format explanation
```

### Configuration Management

#### Environment Detection
```python
import os

def is_production():
    """Check if running in production environment."""
    return (
        os.environ.get("RAILWAY_ENVIRONMENT") == "production" or
        os.environ.get("NODE_ENV") == "production"
    )

def get_config_value(key, default=None, required=False):
    """Get configuration value with validation."""
    value = os.environ.get(key, default)
    
    if required and value is None:
        raise ValueError(f"Required environment variable {key} not set")
    
    return value
```

## 🧪 Testing Strategy

### Test Organization
```
tests/
├── unit/
│   ├── test_handlers.py
│   ├── test_utils.py
│   └── test_models.py
├── integration/
│   ├── test_firebase.py
│   ├── test_bot_integration.py
│   └── test_payment_flow.py
└── e2e/
    ├── test_order_flow.py
    └── test_admin_workflow.py
```

### Testing Patterns

#### Unit Test Example
```python
import unittest
from unittest.mock import patch, MagicMock
from src.handlers.order_handlers import create_order

class TestOrderHandlers(unittest.TestCase):
    
    @patch('src.firebase_db.save_order')
    def test_create_order_success(self, mock_save):
        """Test successful order creation."""
        mock_save.return_value = True
        
        result = create_order(user_id=123, items=['item1', 'item2'])
        
        self.assertTrue(result)
        mock_save.assert_called_once()
```

#### Integration Test Example
```python
import unittest
from src.firebase_db import test_firebase_connectivity

class TestFirebaseIntegration(unittest.TestCase):
    
    def test_firebase_connection(self):
        """Test Firebase connectivity."""
        self.assertTrue(test_firebase_connectivity())
```

## 🚀 Deployment Process

### Pre-Deployment Checklist

#### Code Quality
- [ ] All tests pass
- [ ] Code review completed
- [ ] Documentation updated
- [ ] No debug code or console.log statements
- [ ] Error handling implemented

#### Security
- [ ] No hardcoded credentials
- [ ] Environment variables properly configured
- [ ] Access controls verified
- [ ] Input validation implemented

#### Performance
- [ ] Database queries optimized
- [ ] Memory usage acceptable
- [ ] Response times within limits
- [ ] Resource limits configured

### Deployment Steps

#### 1. Environment Preparation
```bash
# Verify environment variables
docker-compose config

# Test container build
docker-compose build

# Run health checks
docker-compose up --abort-on-container-exit
```

#### 2. Production Deployment
```bash
# Deploy to production
docker-compose -f docker-compose.yml up -d

# Verify deployment
docker-compose ps
docker-compose logs --tail=50
```

#### 3. Post-Deployment Verification
- [ ] All bots responding
- [ ] Firebase connectivity confirmed
- [ ] Health checks passing
- [ ] Logs showing normal operation

## 📊 Monitoring and Logging

### Logging Standards

#### Log Levels
- **DEBUG**: Detailed diagnostic information
- **INFO**: General operational messages
- **WARNING**: Warning messages for unusual situations
- **ERROR**: Error messages for serious problems
- **CRITICAL**: Critical errors requiring immediate attention

#### Log Format
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Usage examples
logger.info(f"User {user_id} started order process")
logger.warning(f"Unusual payment method selected: {method}")
logger.error(f"Firebase connection failed: {error}")
```

### Health Monitoring

#### Health Check Implementation
```python
def health_check():
    """Comprehensive system health check."""
    checks = {
        'firebase': test_firebase_connectivity(),
        'bots': all_bots_responsive(),
        'memory': check_memory_usage(),
        'disk': check_disk_space()
    }
    
    return all(checks.values()), checks
```

## 🔒 Security Best Practices

### Data Protection

#### Sensitive Data Handling
- **Never commit**: API keys, tokens, passwords, credentials
- **Use environment variables**: For all sensitive configuration
- **Encrypt at rest**: Database encryption enabled
- **Encrypt in transit**: HTTPS/TLS for all communications

#### Input Validation
```python
def validate_user_input(data, field_name, max_length=100):
    """Validate and sanitize user input."""
    if not data or not isinstance(data, str):
        raise ValueError(f"Invalid {field_name}")
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', data.strip())
    
    if len(sanitized) > max_length:
        raise ValueError(f"{field_name} too long")
    
    return sanitized
```

### Access Control

#### Authorization Pattern
```python
from src.utils.access_control import require_admin

@require_admin
def admin_only_function(user_id):
    """Function that requires admin privileges."""
    pass

def require_admin(func):
    """Decorator to require admin authorization."""
    def wrapper(*args, **kwargs):
        user_id = extract_user_id(args, kwargs)
        if not is_admin(user_id):
            raise UnauthorizedError("Admin access required")
        return func(*args, **kwargs)
    return wrapper
```

### Error Handling Security

#### Secure Error Messages
```python
def handle_error(error, user_facing=True):
    """Handle errors with appropriate user messages."""
    # Log detailed error for debugging
    logger.error(f"Detailed error: {error}")
    
    if user_facing:
        # Return generic message to user
        return "An error occurred. Please try again later."
    else:
        # Return detailed error for internal use
        return str(error)
```

---

*This document establishes the standards and best practices for the Wiz Aroma Food Delivery System. Following these guidelines ensures code quality, security, and maintainability.*
