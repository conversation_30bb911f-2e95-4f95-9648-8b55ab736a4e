# =============================================================================
# Wiz Aroma Food Delivery System v2.1 - Environment Configuration Template
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control - it contains sensitive data

# =============================================================================
# 🌍 ENVIRONMENT DETECTION
# =============================================================================
# Set to 'production' when deploying to Railway or other cloud platforms
RAILWAY_ENVIRONMENT=development
NODE_ENV=development

# =============================================================================
# 🤖 TELEGRAM BOT TOKENS
# =============================================================================
# Get these tokens from @BotFather on Telegram
# Format: XXXXXXXXXX:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Main user-facing bot
BOT_TOKEN=your_main_bot_token_here

# Administrative bots
ADMIN_BOT_TOKEN=your_admin_bot_token_here
FINANCE_BOT_TOKEN=your_finance_bot_token_here
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here
MANAGEMENT_BOT_TOKEN=your_management_bot_token_here

# Specialized operational bots
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token_here
DELIVERY_BOT_TOKEN=your_delivery_bot_token_here

# =============================================================================
# 🔐 AUTHORIZATION & ACCESS CONTROL
# =============================================================================
# Your Telegram user ID (get from @userinfobot)
SYSTEM_ADMIN_ID=your_telegram_user_id

# Authorized users for each bot (JSON array format)
ADMIN_CHAT_IDS=["your_admin_id"]
ORDER_TRACK_BOT_AUTHORIZED_IDS=["your_admin_id"]
DELIVERY_BOT_AUTHORIZED_IDS=["your_admin_id", "delivery_manager_id"]
MANAGEMENT_BOT_AUTHORIZED_IDS=["your_admin_id", "manager_id"]

# =============================================================================
# 🔥 FIREBASE CONFIGURATION
# =============================================================================
# Firebase Realtime Database URL
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Firebase service account credentials (Base64 encoded for Railway compatibility)
# Option 1: Base64 encoded JSON (recommended for Railway)
FIREBASE_CREDENTIALS="your_base64_encoded_firebase_credentials_here"

# Option 2: File path (for local development)
# FIREBASE_CREDENTIALS_PATH="path/to/your/firebase-credentials.json"

# To generate base64 encoded credentials:
# 1. Download your Firebase service account JSON file
# 2. Run: python -c "import base64, json; print(base64.b64encode(open('your-file.json').read().encode()).decode())"

# =============================================================================
# 💳 PAYMENT INFORMATION
# =============================================================================
# Telebirr (Ethiopian mobile payment)
TELEBIRR_PHONE=+************
TELEBIRR_NAME=Your Business Name

# Commercial Bank of Ethiopia
CBE_ACCOUNT_NUMBER=****************
CBE_ACCOUNT_NAME=Your Business Name

# Bank of Abyssinia
BOA_ACCOUNT_NUMBER=****************
BOA_ACCOUNT_NAME=Your Business Name

# =============================================================================
# 📞 CONTACT & SUPPORT INFORMATION
# =============================================================================
# Primary support contacts
SUPPORT_PHONE_1=+************
SUPPORT_PHONE_2=+************
SUPPORT_TELEGRAM=@your_support_username

# Customer service details
CUSTOMER_SERVICE_PHONE=+************
CUSTOMER_SERVICE_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>

# =============================================================================
# 📧 EMAIL CONFIGURATION (Optional)
# =============================================================================
# For automated email notifications
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password

# =============================================================================
# ⚙️ APPLICATION CONFIGURATION
# =============================================================================
# Logging level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# Test mode (set to True for development without Telegram API)
TEST_MODE=False

# Data refresh intervals (seconds)
WATCHDOG_INTERVAL=30

# =============================================================================
# 🚀 DEPLOYMENT NOTES
# =============================================================================
# For Railway deployment:
# 1. Set RAILWAY_ENVIRONMENT=production
# 2. Add all environment variables in Railway dashboard
# 3. For FIREBASE_CREDENTIALS, use single-line JSON format
# 4. Ensure all bot tokens are properly set
#
# For local development:
# 1. Keep RAILWAY_ENVIRONMENT=development
# 2. You can use Firebase credentials file instead of JSON string
# 3. Set TEST_MODE=True for testing without Telegram API
