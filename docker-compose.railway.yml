# Docker Compose configuration for Railway deployment
# This version uses the pre-built Docker image from Docker Hub

services:
  wiz-aroma-bot:
    image: fedbish/wiz-aroma-food-delivery-ddu:V2.2  # Updated version with Railway fixes
    container_name: wiz-aroma-delivery-bot
    restart: unless-stopped
    environment:
      # Production environment indicators
      - RAILWAY_ENVIRONMENT=production
      - NODE_ENV=production
      # Firebase configuration (set these in Railway environment variables)
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_CREDENTIALS=${FIREBASE_CREDENTIALS}
      # Bot tokens (set these in Railway environment variables)
      - BOT_TOKEN=${BOT_TOKEN}
      - ADMIN_BOT_TOKEN=${ADMIN_BOT_TOKEN}
      - FINANCE_BOT_TOKEN=${FINANCE_BOT_TOKEN}
      - MAINTENANCE_BOT_TOKEN=${MAINTENANCE_BOT_TOKEN}
      - MANAGEMENT_BOT_TOKEN=${MANAGEMENT_BOT_TOKEN}
      - ORDER_TRACK_BOT_TOKEN=${ORDER_TRACK_BOT_TOKEN}
      - DELIVERY_BOT_TOKEN=${DELIVERY_BOT_TOKEN}
      # Authorization IDs
      - SYSTEM_ADMIN_ID=${SYSTEM_ADMIN_ID}
      - ADMIN_CHAT_IDS=${ADMIN_CHAT_IDS}
      - ORDER_TRACK_BOT_AUTHORIZED_IDS=${ORDER_TRACK_BOT_AUTHORIZED_IDS}
      - DELIVERY_BOT_AUTHORIZED_IDS=${DELIVERY_BOT_AUTHORIZED_IDS}
      - MANAGEMENT_BOT_AUTHORIZED_IDS=${MANAGEMENT_BOT_AUTHORIZED_IDS}
      # Payment information
      - TELEBIRR_PHONE=${TELEBIRR_PHONE}
      - TELEBIRR_NAME=${TELEBIRR_NAME}
      - CBE_ACCOUNT_NUMBER=${CBE_ACCOUNT_NUMBER}
      - CBE_ACCOUNT_NAME=${CBE_ACCOUNT_NAME}
      - BOA_ACCOUNT_NUMBER=${BOA_ACCOUNT_NUMBER}
      - BOA_ACCOUNT_NAME=${BOA_ACCOUNT_NAME}
      # Contact information
      - SUPPORT_PHONE_1=${SUPPORT_PHONE_1}
      - SUPPORT_PHONE_2=${SUPPORT_PHONE_2}
      - SUPPORT_TELEGRAM=${SUPPORT_TELEGRAM}
      - CUSTOMER_SERVICE_PHONE=${CUSTOMER_SERVICE_PHONE}
      - CUSTOMER_SERVICE_EMAIL=${CUSTOMER_SERVICE_EMAIL}
      - BUSINESS_EMAIL=${BUSINESS_EMAIL}
      # Application configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TEST_MODE=${TEST_MODE:-False}
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import psutil; exit(0 if any('python' in p.name() for p in psutil.process_iter()) else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Add a separate service for each bot type
  wiz-aroma-user-bot:
    image: fedbish/wiz-aroma-food-delivery-ddu:V2.2
    container_name: wiz-aroma-user-bot
    restart: unless-stopped
    environment:
      - RAILWAY_ENVIRONMENT=production
      - NODE_ENV=production
      # Add all the same environment variables as above
    command: ["python", "main.py", "--bot", "user"]
    profiles:
      - separate-bots

  wiz-aroma-admin-bot:
    image: fedbish/wiz-aroma-food-delivery-ddu:V2.2
    container_name: wiz-aroma-admin-bot
    restart: unless-stopped
    environment:
      - RAILWAY_ENVIRONMENT=production
      - NODE_ENV=production
      # Add all the same environment variables as above
    command: ["python", "main.py", "--bot", "admin"]
    profiles:
      - separate-bots
